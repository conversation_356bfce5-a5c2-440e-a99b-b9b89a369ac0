"""
Asynchronous export utilities for non-blocking export operations.

This module provides optimized implementations for non-blocking exports with:
1. True asynchronous processing that doesn't block the worker
2. Efficient resource management
3. Improved concurrency handling
"""

import asyncio
import os
import time
import uuid
import json
import csv  # Still needed for CSV export functionality
import tempfile
import zipfile
import math
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from typing import AsyncGenerator, Dict, List, Any, Optional, AsyncIterable, Tuple

import pyarrow as pa
import pyarrow.parquet as pq
from fastapi.responses import StreamingResponse
from fastapi import HTTPException
from starlette.background import BackgroundTask

from magic_gateway.core.logging_config import log
from magic_gateway.db.clickhouse_handler import ClickHouseHandler
from magic_gateway.utils.streaming_excel_writer import (
    StreamingExcelWriter,
    EnhancedStreamingResponse,
    OPTIMAL_CHUNK_SIZE,
    get_memory_usage_mb,
    identify_period_column,
)
from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator
from magic_gateway.utils.parquet_processor import (
    get_parquet_file_info,
    read_parquet_file_async_streaming,
)


def clean_excel_value(value: Any) -> Any:
    """
    Clean a value for Excel export by handling NaN, INF, NULL, and NA values.

    Args:
        value: The value to clean

    Returns:
        Cleaned value safe for Excel export
    """
    if value is None:
        return ""

    # Handle numeric NaN and INF values
    if isinstance(value, (int, float)):
        if math.isnan(value) or math.isinf(value):
            return ""

    # Handle string representations of NULL/NA/NaN
    str_value = str(value).strip().lower()
    if str_value in ("null", "na", "nan", "none", ""):
        return ""

    return value


# Dedicated thread pool for CPU-intensive Excel operations
# Limit to a small number to avoid overwhelming the system
# Reduced from 2 to 1 to minimize memory usage per concurrent export
excel_thread_pool = ThreadPoolExecutor(max_workers=2, thread_name_prefix="excel_worker")

# Memory management constants
MAX_MEMORY_CHUNKS = 50  # Maximum number of chunks to hold in memory
CHUNK_MEMORY_THRESHOLD = 100 * 1024 * 1024  # 100MB threshold for chunk processing

# Excel format constraints
EXCEL_MAX_ROWS = 1048576  # Excel's maximum rows per sheet (2^20)
EXCEL_SAFE_ROW_LIMIT = (
    1000000  # Conservative limit to account for headers and formatting
)

# Keep track of active export tasks
active_exports = {}


async def generate_streaming_csv_response(
    data_chunks: AsyncIterable[List[Dict[str, Any]]],
    filename: str,
    columns: Optional[List[str]] = None,
) -> StreamingResponse:
    """
    Generate a streaming ZIP-compressed CSV response from an async iterable of data chunks.

    Args:
        data_chunks: Async iterable yielding chunks of data as lists of dictionaries
        filename: Name of the file to be downloaded
        columns: Optional list of columns to include (and their order)

    Returns:
        StreamingResponse with streaming ZIP content containing CSV
    """
    # Create a temporary file for the CSV content
    temp_csv_file = tempfile.NamedTemporaryFile(
        mode="w+", delete=False, suffix=".csv", encoding="utf-8"
    )
    temp_csv_path = temp_csv_file.name

    try:
        # Generate CSV content to temporary file
        header_written = False
        columns_to_use = columns or []
        total_rows = 0

        async for chunk in data_chunks:
            # If this is the first chunk and we don't have columns specified
            if not columns_to_use and not header_written and chunk:
                # Use the columns from the first row
                columns_to_use = list(chunk[0].keys())

            # Create CSV writer
            writer = csv.DictWriter(temp_csv_file, fieldnames=columns_to_use)

            # Write header only once
            if not header_written:
                writer.writeheader()
                header_written = True

            # Write rows for this chunk
            writer.writerows(chunk)
            total_rows += len(chunk)

            # Yield control back to the event loop
            await asyncio.sleep(0.01)

        temp_csv_file.close()
        log.info(f"Generated CSV with {total_rows} rows for ZIP compression")

        # Create streaming ZIP response
        async def zip_streaming_generator():
            try:
                # Create a temporary ZIP file
                temp_zip_file = tempfile.NamedTemporaryFile(delete=False, suffix=".zip")
                temp_zip_path = temp_zip_file.name
                temp_zip_file.close()

                # Create ZIP file with CSV content
                with zipfile.ZipFile(temp_zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(temp_csv_path, f"{filename}.csv")

                log.info(f"Created ZIP file: {temp_zip_path}")

                # Stream the ZIP file
                with open(temp_zip_path, "rb") as zip_file:
                    while chunk := zip_file.read(OPTIMAL_CHUNK_SIZE):
                        yield chunk
                        # Small delay to allow other tasks to run
                        await asyncio.sleep(0.01)

                # Clean up temporary files
                try:
                    os.unlink(temp_csv_path)
                    os.unlink(temp_zip_path)
                    log.debug("Cleaned up temporary CSV and ZIP files")
                except Exception as e:
                    log.warning(f"Error cleaning up temporary files: {e}")

            except Exception as e:
                log.error(f"Error in ZIP streaming generator: {e}", exc_info=True)
                # Clean up on error
                try:
                    if os.path.exists(temp_csv_path):
                        os.unlink(temp_csv_path)
                    if "temp_zip_path" in locals() and os.path.exists(temp_zip_path):
                        os.unlink(temp_zip_path)
                except Exception:
                    pass
                yield f"Error generating ZIP file: {str(e)}".encode("utf-8")

        # Create response with appropriate headers for ZIP file
        response = StreamingResponse(
            zip_streaming_generator(), media_type="application/zip"
        )
        response.headers["Content-Disposition"] = f"attachment; filename={filename}.zip"
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"

        return response

    except Exception as e:
        # Clean up temporary file on error
        try:
            temp_csv_file.close()
            if os.path.exists(temp_csv_path):
                os.unlink(temp_csv_path)
        except Exception:
            pass

        log.error(f"Error generating CSV response: {e}", exc_info=True)
        raise


async def generate_streaming_parquet_response(
    data_chunks: AsyncIterable[List[Dict[str, Any]]],
    filename: str,
    job_info: Optional[Any] = None,
) -> StreamingResponse:
    """
    Generate a streaming Parquet response from an async iterable of data chunks.

    Args:
        data_chunks: Async iterable yielding chunks of data as lists of dictionaries
        filename: Name of the file to be downloaded
        job_info: Optional job information to include in the file metadata

    Returns:
        StreamingResponse with streaming Parquet content
    """
    # Create a temporary file to store the Parquet data
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
    temp_file_path = temp_file.name
    temp_file.close()

    try:
        # Collect all data first (Parquet requires schema to be known upfront)
        all_data = []
        total_rows = 0
        start_time = time.time()

        async for chunk in data_chunks:
            if chunk:
                all_data.extend(chunk)
                total_rows += len(chunk)

                # Log progress for large datasets
                if total_rows % 100000 == 0:
                    elapsed = time.time() - start_time
                    rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                    log.info(
                        f"Collected {total_rows} rows for Parquet export ({rows_per_sec:.1f} rows/sec)"
                    )

                # Yield control back to the event loop
                await asyncio.sleep(0)

        # Convert to PyArrow Table
        if all_data:
            log.info(
                f"Converting {total_rows} rows to PyArrow Table for Parquet export"
            )

            # Process job_info for metadata
            metadata = {}
            if job_info:
                from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator

                info_generator = InfoSheetGenerator(
                    None
                )  # No workbook needed for processing
                processed_info = info_generator.process_job_info(job_info, filename)
                # Convert all values to strings for metadata
                metadata = {k: str(v) for k, v in processed_info.items()}

            # Convert to PyArrow Table
            table = pa.Table.from_pylist(all_data)

            # Write to Parquet file
            # Note: In PyArrow 20.0.0, we can't directly pass metadata to write_table
            # Instead, we'll set the schema metadata on the table itself
            if metadata:
                # Convert metadata to bytes for PyArrow
                metadata_bytes = {k.encode(): v.encode() for k, v in metadata.items()}
                # Create a new table with the same data but with metadata
                table = table.replace_schema_metadata(metadata_bytes)

            # Write the table to file
            pq.write_table(table, temp_file_path)

            log.info(f"Parquet file created at {temp_file_path} with {total_rows} rows")
        else:
            # Create an empty table with appropriate schema
            log.warning("No data found for Parquet export, creating empty file")
            empty_table = pa.Table.from_pylist([{}] if not all_data else all_data)

            # Add metadata to empty table if available
            if job_info:
                from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator

                info_generator = InfoSheetGenerator(
                    None
                )  # No workbook needed for processing
                processed_info = info_generator.process_job_info(job_info, filename)
                # Convert all values to strings for metadata
                metadata = {k: str(v) for k, v in processed_info.items()}
                # Convert metadata to bytes for PyArrow
                metadata_bytes = {k.encode(): v.encode() for k, v in metadata.items()}
                # Create a new table with the same data but with metadata
                empty_table = empty_table.replace_schema_metadata(metadata_bytes)

            # Write the table to file
            pq.write_table(empty_table, temp_file_path)

        # Create a streaming response
        async def file_streamer():
            try:
                with open(temp_file_path, "rb") as f:
                    while chunk := f.read(OPTIMAL_CHUNK_SIZE):
                        yield chunk
                        # Small delay to allow other tasks to run
                        await asyncio.sleep(0.01)
            finally:
                # Clean up the temporary file after streaming is complete
                try:
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                        log.info(
                            f"Temporary Parquet file {temp_file_path} removed after streaming"
                        )
                except Exception as e:
                    log.warning(
                        f"Failed to remove temporary Parquet file {temp_file_path}: {e}"
                    )

        # Create response with appropriate headers
        response = StreamingResponse(
            file_streamer(),
            media_type="application/octet-stream",
            headers={
                "Content-Disposition": f"attachment; filename={filename}.parquet",
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0",
            },
        )

        return response

    except Exception as e:
        # Clean up the temporary file in case of error
        if os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception:
                pass

        log.error(f"Error generating Parquet response: {e}", exc_info=True)
        raise


# Note: process_job_info function removed - now handled by InfoSheetGenerator


def _estimate_facts_from_job_info(
    job_info: Any, total_raw_rows: int
) -> Optional[Dict[str, Any]]:
    """
    Estimate Excel Facts row count using job_info metadata.

    This provides a more accurate estimation by using the actual number of facts,
    axes, and periods from the job configuration.

    Args:
        job_info: Job information containing facts, axes, periods data
        total_raw_rows: Total raw rows in the dataset

    Returns:
        Dictionary with estimation details or None if estimation not possible
    """
    try:
        # Process job_info to get structured data
        from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator

        info_generator = InfoSheetGenerator(None)  # No workbook needed for processing
        processed_info = info_generator.process_job_info(job_info, "estimation")

        # Extract facts count
        facts_count = 0
        facts_data = processed_info.get("facts", "")
        if facts_data:
            try:
                if isinstance(facts_data, str):
                    # Try to parse as JSON
                    import json

                    try:
                        facts_list = json.loads(facts_data)
                        if isinstance(facts_list, list):
                            facts_count = len(facts_list)
                        elif isinstance(facts_list, dict):
                            facts_count = len(facts_list.keys())
                    except json.JSONDecodeError:
                        # Count comma-separated values
                        facts_count = len(
                            [f.strip() for f in facts_data.split(",") if f.strip()]
                        )
                elif isinstance(facts_data, (list, tuple)):
                    facts_count = len(facts_data)
                elif isinstance(facts_data, dict):
                    facts_count = len(facts_data.keys())
            except Exception:
                pass

        # Extract periods count
        periods_count = 0
        periods_data = processed_info.get("periods", "")
        if periods_data:
            try:
                if isinstance(periods_data, str):
                    # Try to parse as JSON
                    import json

                    try:
                        periods_list = json.loads(periods_data)
                        if isinstance(periods_list, list):
                            periods_count = len(periods_list)
                    except json.JSONDecodeError:
                        # Count comma-separated values
                        periods_count = len(
                            [p.strip() for p in periods_data.split(",") if p.strip()]
                        )
                elif isinstance(periods_data, (list, tuple)):
                    periods_count = len(periods_data)
            except Exception:
                pass

        # If we have facts count, we can estimate unique combinations
        if facts_count > 0:
            # Estimate unique combinations based on raw_rows / facts_count
            estimated_unique_combinations = (
                total_raw_rows // facts_count if facts_count > 0 else total_raw_rows
            )

            # If we also have periods, we can refine the estimate
            if periods_count > 0:
                # Each period might have similar number of unique combinations
                estimated_unique_combinations = (
                    estimated_unique_combinations // periods_count * periods_count
                )

            # Ensure the estimate is reasonable
            estimated_unique_combinations = max(
                1, min(estimated_unique_combinations, total_raw_rows)
            )

            reduction_ratio = (
                1.0 - (estimated_unique_combinations / total_raw_rows)
                if total_raw_rows > 0
                else 0.0
            )

            log.info(
                f"Job info facts estimation: {facts_count} facts, {periods_count} periods -> {estimated_unique_combinations:,} estimated unique combinations"
            )

            return {
                "estimated_rows": estimated_unique_combinations,
                "facts_count": facts_count,
                "periods_count": periods_count,
                "reduction_ratio": reduction_ratio,
                "estimation_method": "job_info",
            }

    except Exception as e:
        log.debug(f"Could not estimate from job_info: {e}")

    return None


async def estimate_excel_facts_row_count(
    parquet_file_path: str, job_info: Optional[Any] = None
) -> Dict[str, Any]:
    """
    Estimate the row count after horizontal facts transformation.

    For Excel Facts format, data is transformed from vertical (many rows per fact)
    to horizontal (one row per unique combination of index columns), which typically
    reduces row count by 80-95%.

    Args:
        parquet_file_path: Path to the Parquet file to analyze

    Returns:
        Dictionary containing:
        - estimated_rows: int - Estimated rows after facts transformation
        - raw_rows: int - Original row count
        - reduction_ratio: float - Ratio of reduction (0.0-1.0)
        - unique_combinations: int - Number of unique index combinations
        - fact_column: str - Identified fact column
        - value_column: str - Identified value column
    """
    try:
        log.info(f"Estimating Excel Facts row count for: {parquet_file_path}")

        # Get total row count from file metadata first
        file_info = get_parquet_file_info(parquet_file_path)
        total_raw_rows = file_info["num_rows"]
        log.info(f"Total rows in file: {total_raw_rows:,}")

        # Try to get better estimation from job_info if available
        job_info_estimation = None
        if job_info:
            job_info_estimation = _estimate_facts_from_job_info(
                job_info, total_raw_rows
            )
            if job_info_estimation:
                log.info(
                    f"Job info estimation: {job_info_estimation['estimated_rows']:,} facts rows based on job metadata"
                )

        # Sample the data to identify fact/value columns and estimate unique combinations
        unique_combinations = set()
        fact_column = None
        value_column = None
        index_columns = []
        sample_size = 0

        # Read a sample of the data to analyze structure
        async for chunk in read_parquet_file_async_streaming(
            parquet_file_path, chunk_size=50000
        ):
            if not chunk:
                continue

            # Identify fact and value columns from first chunk
            if fact_column is None and chunk:
                from magic_gateway.utils.streaming_excel_writer import (
                    identify_fact_value_columns,
                    identify_index_columns,
                )

                first_row = chunk[0]
                fact_column, value_column = identify_fact_value_columns(first_row)

                if fact_column and value_column:
                    index_columns = identify_index_columns(
                        first_row, fact_column, value_column
                    )
                    log.info(
                        f"Identified columns - fact: {fact_column}, value: {value_column}, index: {index_columns}"
                    )
                else:
                    log.warning(
                        "Could not identify fact/value columns for Excel Facts estimation"
                    )
                    # Fallback: assume no transformation (worst case)
                    return {
                        "estimated_rows": total_raw_rows,
                        "raw_rows": total_raw_rows,
                        "reduction_ratio": 0.0,
                        "unique_combinations": total_raw_rows,
                        "fact_column": None,
                        "value_column": None,
                        "index_columns": [],
                    }

            # Count unique combinations of index columns
            if index_columns:
                for row in chunk:
                    index_key = tuple(str(row.get(col, "")) for col in index_columns)
                    unique_combinations.add(index_key)

            sample_size += len(chunk)

            # Stop sampling after reasonable amount for estimation
            if sample_size >= 200000:  # Sample up to 200K rows for estimation
                break

        # Use job_info estimation if available and seems reasonable, otherwise use sampling
        if job_info_estimation and job_info_estimation["estimated_rows"] > 0:
            # Use job_info estimation as it's likely more accurate
            estimated_unique = job_info_estimation["estimated_rows"]
            reduction_ratio = job_info_estimation["reduction_ratio"]
            estimation_method = "job_info"
            log.info(
                f"Using job_info estimation: {estimated_unique:,} facts rows ({reduction_ratio:.1%} reduction)"
            )
        else:
            # Fall back to sampling-based estimation
            if sample_size >= total_raw_rows:
                estimated_unique = len(unique_combinations)
                log.info(
                    f"Analyzed complete dataset: {total_raw_rows} raw rows, {estimated_unique} unique combinations"
                )
            else:
                # Estimate total unique combinations based on sample
                sample_ratio = sample_size / total_raw_rows
                estimated_unique = int(len(unique_combinations) / sample_ratio)
                log.info(
                    f"Estimated from sample: {sample_size}/{total_raw_rows} rows, {len(unique_combinations)} sample unique -> {estimated_unique} estimated total unique"
                )

            # Calculate reduction ratio
            reduction_ratio = (
                1.0 - (estimated_unique / total_raw_rows) if total_raw_rows > 0 else 0.0
            )
            estimation_method = "sampling"

        log.info(
            f"Excel Facts estimation ({estimation_method}): {total_raw_rows:,} raw rows -> {estimated_unique:,} facts rows ({reduction_ratio:.1%} reduction)"
        )

        return {
            "estimated_rows": estimated_unique,
            "raw_rows": total_raw_rows,
            "reduction_ratio": reduction_ratio,
            "unique_combinations": estimated_unique,
            "fact_column": fact_column,
            "value_column": value_column,
            "index_columns": index_columns,
        }

    except Exception as e:
        log.error(f"Error estimating Excel Facts row count: {e}", exc_info=True)
        # Return conservative estimate on error
        file_info = get_parquet_file_info(parquet_file_path)
        raw_rows = file_info.get("num_rows", 0)
        return {
            "estimated_rows": raw_rows,  # Conservative: assume no reduction
            "raw_rows": raw_rows,
            "reduction_ratio": 0.0,
            "unique_combinations": raw_rows,
            "fact_column": None,
            "value_column": None,
            "index_columns": [],
            "error": str(e),
        }


async def check_excel_row_limit(
    parquet_file_path: str, format_type: str = "excel", job_info: Optional[Any] = None
) -> Dict[str, Any]:
    """
    Check if a Parquet file exceeds Excel's row limits, accounting for format transformations.

    Args:
        parquet_file_path: Path to the Parquet file to check
        format_type: Export format type ('excel', 'excel_facts', etc.)

    Returns:
        Dictionary containing:
        - exceeds_limit: bool - True if file exceeds Excel limits
        - total_rows: int - Total number of rows that will be in Excel (after transformation)
        - raw_rows: int - Original row count in Parquet file
        - excel_compatible: bool - True if file can fit in Excel
        - reason: str - Explanation of the result
        - facts_estimation: Dict - Details of facts transformation (if applicable)
    """
    try:
        # Get basic file info
        file_info = get_parquet_file_info(parquet_file_path)
        raw_rows = file_info["num_rows"]

        # For Excel Facts format, estimate the transformed row count
        if format_type.lower() == "excel_facts":
            log.info(f"Analyzing Excel Facts format for {parquet_file_path}")
            facts_estimation = await estimate_excel_facts_row_count(
                parquet_file_path, job_info
            )
            effective_rows = facts_estimation["estimated_rows"]

            # Check against Excel limits using transformed row count
            exceeds_limit = effective_rows > EXCEL_SAFE_ROW_LIMIT
            excel_compatible = effective_rows <= EXCEL_SAFE_ROW_LIMIT

            reduction_ratio = facts_estimation["reduction_ratio"]

            if exceeds_limit:
                reason = f"Excel Facts format: {raw_rows:,} raw rows -> {effective_rows:,} facts rows ({reduction_ratio:.1%} reduction), still exceeding Excel's safe limit of {EXCEL_SAFE_ROW_LIMIT:,} rows"
            else:
                reason = f"Excel Facts format: {raw_rows:,} raw rows -> {effective_rows:,} facts rows ({reduction_ratio:.1%} reduction), within Excel's limit of {EXCEL_SAFE_ROW_LIMIT:,} rows"

            log.info(f"Excel Facts compatibility check: {reason}")

            return {
                "exceeds_limit": exceeds_limit,
                "total_rows": effective_rows,
                "raw_rows": raw_rows,
                "excel_compatible": excel_compatible,
                "reason": reason,
                "file_info": file_info,
                "facts_estimation": facts_estimation,
                "format_type": format_type,
            }
        else:
            # Standard Excel format - use raw row count
            exceeds_limit = raw_rows > EXCEL_SAFE_ROW_LIMIT
            excel_compatible = raw_rows <= EXCEL_SAFE_ROW_LIMIT

            if exceeds_limit:
                reason = f"Standard Excel format: {raw_rows:,} rows, exceeding Excel's safe limit of {EXCEL_SAFE_ROW_LIMIT:,} rows"
            else:
                reason = f"Standard Excel format: {raw_rows:,} rows, within Excel's limit of {EXCEL_SAFE_ROW_LIMIT:,} rows"

            log.info(f"Excel compatibility check: {reason}")

            return {
                "exceeds_limit": exceeds_limit,
                "total_rows": raw_rows,
                "raw_rows": raw_rows,
                "excel_compatible": excel_compatible,
                "reason": reason,
                "file_info": file_info,
                "facts_estimation": None,
                "format_type": format_type,
            }

    except Exception as e:
        log.error(
            f"Error checking Excel row limit for {parquet_file_path}: {e}",
            exc_info=True,
        )
        # Return conservative result on error
        return {
            "exceeds_limit": True,
            "total_rows": 0,
            "raw_rows": 0,
            "excel_compatible": False,
            "reason": f"Error reading file metadata: {str(e)}",
            "file_info": None,
            "facts_estimation": None,
            "format_type": format_type,
        }


async def analyze_period_based_excel_compatibility(
    parquet_file_path: str, format_type: str = "excel"
) -> Dict[str, Any]:
    """
    Analyze if splitting data by periods would make it Excel-compatible.

    Args:
        parquet_file_path: Path to the Parquet file to analyze
        format_type: Export format type ('excel', 'excel_facts', etc.)

    Returns:
        Dictionary containing:
        - period_split_viable: bool - True if period splitting would work
        - period_analysis: Dict - Analysis of each period's row count
        - largest_period_rows: int - Row count of the largest period (after transformation)
        - total_periods: int - Number of unique periods found
        - recommendation: str - Recommended action
        - format_type: str - Format type used for analysis
        - facts_analysis: Dict - Excel Facts analysis per period (if applicable)
    """
    try:
        log.info(
            f"Analyzing period-based Excel compatibility for: {parquet_file_path} (format: {format_type})"
        )

        # First check if the file has a period column
        # Read a small sample to identify period column
        sample_chunks = []
        chunk_count = 0
        period_column = None
        fact_column = None
        value_column = None
        index_columns = []

        async for chunk in read_parquet_file_async_streaming(
            parquet_file_path, chunk_size=10000
        ):
            if not chunk:
                continue

            sample_chunks.append(chunk)
            chunk_count += 1

            # Identify period column from first chunk
            if period_column is None and chunk:
                headers = list(chunk[0].keys())
                period_column = identify_period_column(headers)
                log.info(f"Identified period column: {period_column}")

                # For Excel Facts, also identify fact/value columns
                if format_type.lower() == "excel_facts":
                    from magic_gateway.utils.streaming_excel_writer import (
                        identify_fact_value_columns,
                        identify_index_columns,
                    )

                    first_row = chunk[0]
                    fact_column, value_column = identify_fact_value_columns(first_row)
                    if fact_column and value_column:
                        index_columns = identify_index_columns(
                            first_row, fact_column, value_column
                        )
                        log.info(
                            f"Excel Facts columns - fact: {fact_column}, value: {value_column}, index: {index_columns}"
                        )

            # Only analyze first few chunks for efficiency
            if chunk_count >= 3:
                break

        if not period_column:
            log.info("No period column found - period splitting not viable")
            return {
                "period_split_viable": False,
                "period_analysis": {},
                "largest_period_rows": 0,
                "total_periods": 0,
                "recommendation": "No period column identified - cannot split by periods",
                "format_type": format_type,
                "facts_analysis": None,
            }

        # Now analyze the full dataset by periods
        log.info(f"Analyzing full dataset by period column: {period_column}")
        period_row_counts = {}
        period_unique_combinations = {}  # For Excel Facts analysis
        total_rows_analyzed = 0

        async for chunk in read_parquet_file_async_streaming(
            parquet_file_path, chunk_size=50000
        ):
            if not chunk:
                continue

            # Count rows for each period in this chunk
            for row in chunk:
                period_value = str(row.get(period_column, "Unknown"))
                period_row_counts[period_value] = (
                    period_row_counts.get(period_value, 0) + 1
                )
                total_rows_analyzed += 1

                # For Excel Facts, also track unique combinations per period
                if format_type.lower() == "excel_facts" and index_columns:
                    if period_value not in period_unique_combinations:
                        period_unique_combinations[period_value] = set()

                    index_key = tuple(str(row.get(col, "")) for col in index_columns)
                    period_unique_combinations[period_value].add(index_key)

        # Analyze the results
        if not period_row_counts:
            return {
                "period_split_viable": False,
                "period_analysis": {},
                "largest_period_rows": 0,
                "total_periods": 0,
                "recommendation": "No period data found in dataset",
                "format_type": format_type,
                "facts_analysis": None,
            }

        # For Excel Facts, use unique combinations count; for standard Excel, use raw row count
        if format_type.lower() == "excel_facts" and period_unique_combinations:
            # Calculate effective rows per period after facts transformation
            period_effective_rows = {}
            for period, unique_combos in period_unique_combinations.items():
                period_effective_rows[period] = len(unique_combos)

            largest_period_rows = max(period_effective_rows.values())

            # Create detailed analysis with both raw and effective counts
            period_analysis = {}
            facts_analysis = {}
            for period in period_row_counts.keys():
                raw_count = period_row_counts[period]
                effective_count = period_effective_rows.get(period, raw_count)
                reduction_ratio = (
                    1.0 - (effective_count / raw_count) if raw_count > 0 else 0.0
                )

                period_analysis[period] = {
                    "row_count": effective_count,  # Use effective count for compatibility check
                    "raw_row_count": raw_count,
                    "excel_compatible": effective_count <= EXCEL_SAFE_ROW_LIMIT,
                    "percentage_of_total": (raw_count / total_rows_analyzed) * 100
                    if total_rows_analyzed > 0
                    else 0,
                    "reduction_ratio": reduction_ratio,
                }

                facts_analysis[period] = {
                    "raw_rows": raw_count,
                    "unique_combinations": effective_count,
                    "reduction_ratio": reduction_ratio,
                }

            # Check if largest effective period fits within Excel limits
            period_split_viable = largest_period_rows <= EXCEL_SAFE_ROW_LIMIT

            # Generate recommendation
            if period_split_viable:
                recommendation = f"Excel Facts period splitting viable - largest period has {largest_period_rows:,} facts rows (within Excel limit)"
            else:
                recommendation = f"Excel Facts period splitting not viable - largest period has {largest_period_rows:,} facts rows (exceeds Excel limit of {EXCEL_SAFE_ROW_LIMIT:,})"

            log.info(
                f"Excel Facts period analysis complete: {len(period_row_counts)} periods, largest effective: {largest_period_rows:,} rows, viable: {period_split_viable}"
            )

        else:
            # Standard Excel format - use raw row counts
            largest_period_rows = max(period_row_counts.values())

            # Check if largest period fits within Excel limits
            period_split_viable = largest_period_rows <= EXCEL_SAFE_ROW_LIMIT

            # Create detailed analysis
            period_analysis = {}
            for period, row_count in period_row_counts.items():
                period_analysis[period] = {
                    "row_count": row_count,
                    "excel_compatible": row_count <= EXCEL_SAFE_ROW_LIMIT,
                    "percentage_of_total": (row_count / total_rows_analyzed) * 100
                    if total_rows_analyzed > 0
                    else 0,
                }

            # Generate recommendation
            if period_split_viable:
                recommendation = f"Period splitting viable - largest period has {largest_period_rows:,} rows (within Excel limit)"
            else:
                recommendation = f"Period splitting not viable - largest period has {largest_period_rows:,} rows (exceeds Excel limit of {EXCEL_SAFE_ROW_LIMIT:,})"

            log.info(
                f"Period analysis complete: {len(period_row_counts)} periods, largest: {largest_period_rows:,} rows, viable: {period_split_viable}"
            )
            facts_analysis = None

        total_periods = len(period_row_counts)

        return {
            "period_split_viable": period_split_viable,
            "period_analysis": period_analysis,
            "largest_period_rows": largest_period_rows,
            "total_periods": total_periods,
            "recommendation": recommendation,
            "period_column": period_column,
            "total_rows_analyzed": total_rows_analyzed,
            "format_type": format_type,
            "facts_analysis": facts_analysis,
        }

    except Exception as e:
        log.error(
            f"Error analyzing period-based Excel compatibility: {e}", exc_info=True
        )
        return {
            "period_split_viable": False,
            "period_analysis": {},
            "largest_period_rows": 0,
            "total_periods": 0,
            "recommendation": f"Error during analysis: {str(e)}",
            "period_column": None,
            "total_rows_analyzed": 0,
        }


class ExportFormatDecision:
    """Class to represent an export format decision."""

    def __init__(
        self,
        recommended_format: str,
        use_period_split: bool = False,
        reason: str = "",
        original_format: str = "",
        format_changed: bool = False,
        analysis_data: Optional[Dict[str, Any]] = None,
    ):
        self.recommended_format = recommended_format
        self.use_period_split = use_period_split
        self.reason = reason
        self.original_format = original_format
        self.format_changed = format_changed
        self.analysis_data = analysis_data or {}


async def determine_optimal_export_format(
    parquet_file_path: str,
    requested_format: str,
    allow_format_change: bool = True,
    job_info: Optional[Any] = None,
    separate_periods: Optional[bool] = None,
) -> ExportFormatDecision:
    """
    Determine the optimal export format based on data size constraints.

    This function implements intelligent format selection by:
    1. Checking if data fits within Excel row limits
    2. If not, analyzing if period-based splitting would work
    3. If period splitting won't work, recommending CSV fallback
    4. Respecting explicit user preferences for period separation

    Args:
        parquet_file_path: Path to the Parquet file to analyze
        requested_format: The originally requested format ('excel', 'excel_facts', 'csv', 'parquet')
        allow_format_change: Whether to allow automatic format changes
        job_info: Optional job information for better estimation
        separate_periods: Explicit user preference for period separation (None=auto, True=force, False=disable)

    Returns:
        ExportFormatDecision with the recommended format and reasoning
    """
    try:
        log.info(
            f"Determining optimal export format for {requested_format} from file: {parquet_file_path}"
        )

        # If requested format is not Excel-based, no analysis needed
        if requested_format.lower() not in ["excel", "excel_facts"]:
            log.info(
                f"Requested format '{requested_format}' is not Excel-based, no analysis needed"
            )
            return ExportFormatDecision(
                recommended_format=requested_format,
                use_period_split=False,
                reason=f"Requested format '{requested_format}' does not require size analysis",
                original_format=requested_format,
                format_changed=False,
            )

        # Check for explicit user preference for period separation
        if separate_periods is True:
            log.info(
                "User explicitly requested period separation - honoring preference"
            )
            return ExportFormatDecision(
                recommended_format=requested_format,
                use_period_split=True,
                reason="User explicitly requested period separation (separate_periods=true)",
                original_format=requested_format,
                format_changed=False,
                analysis_data={"user_preference": "separate_periods=true"},
            )

        # Step 1: Check basic Excel row limit compatibility
        log.info("Step 1: Checking Excel row limit compatibility")
        excel_check = await check_excel_row_limit(
            parquet_file_path, requested_format, job_info
        )

        if excel_check["excel_compatible"]:
            # Data fits in Excel without any special handling
            log.info(f"Data is Excel-compatible: {excel_check['reason']}")

            # Check if user explicitly disabled period separation
            if separate_periods is False:
                log.info(
                    "User explicitly disabled period separation - using single sheet"
                )
                reason_suffix = " (user disabled period separation)"
            else:
                reason_suffix = ""

            return ExportFormatDecision(
                recommended_format=requested_format,
                use_period_split=False,
                reason=f"Standard Excel export viable - {excel_check['reason']}{reason_suffix}",
                original_format=requested_format,
                format_changed=False,
                analysis_data={"excel_check": excel_check},
            )

        # Step 2: Data exceeds Excel limits, check if period splitting would help
        # Handle user explicitly disabled period separation
        if separate_periods is False:
            log.info("Data exceeds Excel limits but user disabled period separation")
            log.info("Checking if period-based splitting would resolve the issue")

            # Check if period splitting would work as a fallback
            period_analysis = await analyze_period_based_excel_compatibility(
                parquet_file_path, requested_format
            )

            if period_analysis["period_split_viable"]:
                # Period splitting would work - override user preference to avoid CSV fallback
                log.info(
                    "Period-based splitting viable - overriding user preference to prevent CSV fallback"
                )
                log.info(
                    f"Period splitting analysis: {period_analysis['recommendation']}"
                )
                return ExportFormatDecision(
                    recommended_format=requested_format,
                    use_period_split=True,  # Override user preference
                    reason=f"Period splitting enabled (overriding user preference) - {period_analysis['recommendation']}. Data size requires splitting to stay within Excel format.",
                    original_format=requested_format,
                    format_changed=False,
                    analysis_data={
                        "excel_check": excel_check,
                        "period_analysis": period_analysis,
                        "user_preference_overridden": True,
                    },
                )
            else:
                # Period splitting wouldn't work either - fall back to CSV
                log.info(
                    "Period-based splitting not viable - proceeding with CSV fallback"
                )
                if not allow_format_change:
                    log.warning("Excel format not viable and format change not allowed")
                    return ExportFormatDecision(
                        recommended_format=requested_format,
                        use_period_split=False,
                        reason=f"Excel format not optimal ({excel_check['reason']}) and period splitting not viable, but format change not allowed",
                        original_format=requested_format,
                        format_changed=False,
                        analysis_data={
                            "excel_check": excel_check,
                            "period_analysis": period_analysis,
                        },
                    )
                else:
                    log.info("Recommending CSV fallback - period splitting not viable")
                    return ExportFormatDecision(
                        recommended_format="csv",
                        use_period_split=False,
                        reason=f"CSV fallback - data exceeds Excel limits and period splitting not viable. {excel_check['reason']}",
                        original_format=requested_format,
                        format_changed=True,
                        analysis_data={
                            "excel_check": excel_check,
                            "period_analysis": period_analysis,
                        },
                    )

        log.info("Step 2: Data exceeds Excel limits, analyzing period-based splitting")
        period_analysis = await analyze_period_based_excel_compatibility(
            parquet_file_path, requested_format
        )

        if period_analysis["period_split_viable"]:
            # Period splitting would make the data Excel-compatible
            log.info(f"Period splitting is viable: {period_analysis['recommendation']}")
            return ExportFormatDecision(
                recommended_format=requested_format,
                use_period_split=True,
                reason=f"Excel export with period splitting - {period_analysis['recommendation']}",
                original_format=requested_format,
                format_changed=False,
                analysis_data={
                    "excel_check": excel_check,
                    "period_analysis": period_analysis,
                },
            )

        # Handle case where user explicitly requested period separation but data is too large
        if separate_periods is True:
            log.info(
                "User requested period separation but data exceeds Excel limits even with splitting"
            )
            if not allow_format_change:
                log.warning(
                    "Excel format not viable even with period splitting, but format change not allowed"
                )
                return ExportFormatDecision(
                    recommended_format=requested_format,
                    use_period_split=True,  # Honor user request even if not viable
                    reason=f"Excel format not optimal (user requested period separation but {period_analysis['recommendation']}) but format change not allowed",
                    original_format=requested_format,
                    format_changed=False,
                    analysis_data={
                        "excel_check": excel_check,
                        "period_analysis": period_analysis,
                    },
                )
            else:
                log.info(
                    "Recommending CSV fallback - period separation requested but data too large"
                )
                return ExportFormatDecision(
                    recommended_format="csv",
                    use_period_split=False,
                    reason=f"CSV fallback - user requested period separation but data exceeds Excel limits even with splitting. {period_analysis['recommendation']}",
                    original_format=requested_format,
                    format_changed=True,
                    analysis_data={
                        "excel_check": excel_check,
                        "period_analysis": period_analysis,
                    },
                )

        # Step 3: Neither standard nor period-split Excel will work
        log.info("Step 3: Excel format not viable, considering CSV fallback")

        if not allow_format_change:
            # Format change not allowed, return Excel anyway with warning
            log.warning("Excel format not viable but format change not allowed")
            return ExportFormatDecision(
                recommended_format=requested_format,
                use_period_split=True,  # Try period split as best effort
                reason=f"Excel format not optimal ({excel_check['reason']}, {period_analysis['recommendation']}) but format change not allowed",
                original_format=requested_format,
                format_changed=False,
                analysis_data={
                    "excel_check": excel_check,
                    "period_analysis": period_analysis,
                },
            )

        # Recommend CSV fallback
        log.info("Recommending CSV fallback due to data size constraints")
        return ExportFormatDecision(
            recommended_format="csv",
            use_period_split=False,
            reason=f"Automatic CSV fallback - data too large for Excel ({excel_check['reason']}, {period_analysis['recommendation']})",
            original_format=requested_format,
            format_changed=True,
            analysis_data={
                "excel_check": excel_check,
                "period_analysis": period_analysis,
            },
        )

    except Exception as e:
        log.error(f"Error determining optimal export format: {e}", exc_info=True)
        # Return conservative decision on error
        return ExportFormatDecision(
            recommended_format="csv" if allow_format_change else requested_format,
            use_period_split=False,
            reason=f"Error during analysis, using safe fallback: {str(e)}",
            original_format=requested_format,
            format_changed=allow_format_change
            and requested_format.lower() in ["excel", "excel_facts"],
            analysis_data={"error": str(e)},
        )


def generate_user_feedback_message(format_decision: ExportFormatDecision) -> str:
    """
    Generate a user-friendly feedback message about the export format decision.

    Args:
        format_decision: The format decision made by the intelligent selection

    Returns:
        User-friendly message explaining the export format choice
    """
    if not format_decision.format_changed:
        # No format change, provide confirmation
        if format_decision.use_period_split:
            return f"Export will use {format_decision.recommended_format.upper()} format with separate sheets for each period to accommodate the data size."
        else:
            return f"Export will use the requested {format_decision.recommended_format.upper()} format."

    # Format was changed, explain why
    if format_decision.recommended_format.lower() == "csv":
        analysis_data = format_decision.analysis_data
        excel_check = analysis_data.get("excel_check", {})
        period_analysis = analysis_data.get("period_analysis", {})

        total_rows = excel_check.get("total_rows", 0)
        raw_rows = excel_check.get("raw_rows", total_rows)
        largest_period = period_analysis.get("largest_period_rows", 0)
        total_periods = period_analysis.get("total_periods", 0)
        format_type = excel_check.get("format_type", "excel")
        facts_estimation = excel_check.get("facts_estimation")

        message = f"Export format automatically changed from {format_decision.original_format.upper()} to CSV due to data size constraints:\n"

        # Show different messages for Excel Facts vs standard Excel
        if format_type.lower() == "excel_facts" and facts_estimation:
            reduction_ratio = facts_estimation.get("reduction_ratio", 0.0)
            message += f"• Dataset contains {raw_rows:,} raw rows -> {total_rows:,} facts rows ({reduction_ratio:.1%} reduction)\n"
            message += f"• Even after facts transformation, {total_rows:,} rows exceed Excel's limit of {EXCEL_SAFE_ROW_LIMIT:,} rows\n"
        else:
            message += f"• Dataset contains {total_rows:,} rows, exceeding Excel's limit of {EXCEL_SAFE_ROW_LIMIT:,} rows\n"

        if total_periods > 0:
            if format_type.lower() == "excel_facts":
                message += f"• Data has {total_periods} periods, but the largest period contains {largest_period:,} facts rows\n"
            else:
                message += f"• Data has {total_periods} periods, but the largest period contains {largest_period:,} rows\n"
            message += "• Even with period-based splitting, individual periods exceed Excel's row limit\n"
        else:
            message += (
                "• No period column found for splitting data into manageable chunks\n"
            )

        message += (
            "• CSV format can handle datasets of any size and will preserve all data"
        )

        return message

    return f"Export format changed from {format_decision.original_format.upper()} to {format_decision.recommended_format.upper()}: {format_decision.reason}"


def log_export_decision_details(format_decision: ExportFormatDecision, filename: str):
    """
    Log detailed information about the export format decision.

    Args:
        format_decision: The format decision made
        filename: Name of the file being exported
    """
    log.info(f"=== Export Format Decision for {filename} ===")
    log.info(f"Original format requested: {format_decision.original_format}")
    log.info(f"Recommended format: {format_decision.recommended_format}")
    log.info(f"Format changed: {format_decision.format_changed}")
    log.info(f"Use period split: {format_decision.use_period_split}")
    log.info(f"Reason: {format_decision.reason}")

    # Log analysis data if available
    analysis_data = format_decision.analysis_data
    if analysis_data:
        excel_check = analysis_data.get("excel_check")
        if excel_check:
            log.info(f"Excel compatibility check:")
            log.info(f"  - Total rows: {excel_check.get('total_rows', 'unknown'):,}")
            log.info(
                f"  - Excel compatible: {excel_check.get('excel_compatible', 'unknown')}"
            )
            log.info(
                f"  - Exceeds limit: {excel_check.get('exceeds_limit', 'unknown')}"
            )

        period_analysis = analysis_data.get("period_analysis")
        if period_analysis:
            log.info(f"Period analysis:")
            log.info(
                f"  - Period split viable: {period_analysis.get('period_split_viable', 'unknown')}"
            )
            log.info(
                f"  - Total periods: {period_analysis.get('total_periods', 'unknown')}"
            )
            log.info(
                f"  - Largest period rows: {period_analysis.get('largest_period_rows', 'unknown'):,}"
            )
            log.info(
                f"  - Period column: {period_analysis.get('period_column', 'none')}"
            )

            # Log individual period details
            period_details = period_analysis.get("period_analysis", {})
            if period_details:
                log.debug("Individual period breakdown:")
                for period, details in period_details.items():
                    row_count = details.get("row_count", 0)
                    compatible = details.get("excel_compatible", False)
                    percentage = details.get("percentage_of_total", 0)
                    log.debug(
                        f"  - {period}: {row_count:,} rows ({percentage:.1f}%), Excel compatible: {compatible}"
                    )

    log.info("=== End Export Format Decision ===")


async def non_blocking_clickhouse_stream(
    query: str,
    query_id: str,
    chunk_size: int = 100000,
) -> AsyncGenerator[List[Dict[str, Any]], None]:
    """
    Stream data from ClickHouse without blocking the worker thread.

    This function yields control back to the event loop between chunks,
    allowing other requests to be processed while streaming continues.

    Args:
        query: SQL query to execute
        query_id: Query ID for tracking
        chunk_size: Number of rows to process at a time

    Yields:
        Chunks of data as lists of dictionaries
    """
    total_rows = 0
    start_time = time.time()

    try:
        # Stream data in chunks
        async for chunk in ClickHouseHandler.stream_query_results(
            query=query,
            query_id=query_id,
            chunk_size=chunk_size,
        ):
            if chunk:
                total_rows += len(chunk)

                # Log progress for large datasets
                if total_rows % 100000 == 0:
                    elapsed = time.time() - start_time
                    rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                    log.info(
                        f"Processed {total_rows} rows ({rows_per_sec:.1f} rows/sec)"
                    )

                # Yield the chunk
                yield chunk

                # Yield control back to the event loop to allow other tasks to run
                await asyncio.sleep(0)

    except Exception as e:
        log.error(f"Error in non-blocking ClickHouse stream: {e}", exc_info=True)
        # Return an empty chunk on error
        yield []

    finally:
        # Log completion
        elapsed = time.time() - start_time
        rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
        log.info(
            f"ClickHouse streaming completed: {total_rows} rows in {elapsed:.2f}s "
            f"({rows_per_sec:.1f} rows/sec)"
        )


def create_excel_file_in_thread(
    data_chunks: List[List[Dict[str, Any]]],
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
) -> str:
    """
    Create an Excel file in a separate thread.

    This function is designed to be run in a thread pool to avoid
    blocking the event loop with CPU-intensive Excel operations.

    Args:
        data_chunks: List of data chunks to write to Excel
        filename: Name of the file
        job_info: Optional job information to include
        horizontal_facts: Whether to use horizontal facts format

    Returns:
        Path to the created Excel file
    """
    try:
        start_time = time.time()
        log.info(
            f"Starting Excel generation for {filename} in thread (horizontal_facts={horizontal_facts})"
        )

        # Create Excel writer with horizontal facts option if specified
        excel_writer = StreamingExcelWriter(filename, horizontal_facts=horizontal_facts)

        # Write data chunks
        total_rows = 0
        for chunk in data_chunks:
            if chunk:
                excel_writer.write_data_chunk(chunk)
                total_rows += len(chunk)

        # Add job info if provided
        if job_info:
            # Process job_info is not needed here since we're already in the module that defines it
            # and add_info_sheet will call process_job_info internally
            excel_writer.add_info_sheet(job_info)

        # Save the file - the save method returns the path
        temp_file_path = excel_writer.save()

        # Log completion
        elapsed = time.time() - start_time
        rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
        log.info(
            f"Excel generation completed in thread: {total_rows} rows in {elapsed:.2f}s "
            f"({rows_per_sec:.1f} rows/sec)"
        )

        return temp_file_path

    except Exception as e:
        log.error(f"Error creating Excel file in thread: {e}", exc_info=True)
        raise


async def generate_streaming_excel_response(
    data_iterator: AsyncIterable[List[Dict[str, Any]]],
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
    cleanup_source_file_path: Optional[str] = None,
) -> StreamingResponse:
    """
    Generate an Excel response using true streaming approach without storing all data in memory.

    This function processes data chunks as they arrive and streams the Excel file directly
    to the client without accumulating all data in memory first.

    Args:
        data_iterator: Async iterable yielding chunks of data
        filename: Name of the file
        job_info: Optional job information to include
        horizontal_facts: Whether to use horizontal facts format for Excel
        cleanup_source_file_path: Optional path to source file to clean up after streaming

    Returns:
        StreamingResponse that starts downloading immediately
    """
    # Generate a unique ID for this export
    export_id = str(uuid.uuid4())

    log.info(f"Starting streaming Excel export {export_id} for {filename}")

    # Create a queue for communication between tasks
    queue = asyncio.Queue()

    # Variable to store excel_writer for cleanup
    excel_writer_for_cleanup = None

    # Background task to process data and generate Excel file
    async def process_data_streaming():
        try:
            log.info(f"Starting streaming data processing for export {export_id}")
            start_time = time.time()
            total_rows = 0

            # Create streaming Excel writer
            excel_writer = StreamingExcelWriter(
                filename, horizontal_facts=horizontal_facts
            )

            # Store reference for cleanup
            nonlocal excel_writer_for_cleanup
            excel_writer_for_cleanup = excel_writer

            try:
                # Add info sheet if job info is provided
                if job_info:
                    excel_writer.add_info_sheet(job_info)

                # Process data iterator in streaming fashion
                async for chunk in data_iterator:
                    if chunk:
                        chunk_size = len(chunk)
                        total_rows += chunk_size

                        # Write chunk directly to Excel without storing in memory
                        excel_writer.write_data_chunk(chunk)

                        # Log progress periodically
                        if total_rows % 100000 == 0:
                            elapsed = time.time() - start_time
                            rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                            current_memory_mb = get_memory_usage_mb()
                            log.info(
                                f"Export {export_id}: Processed {total_rows} rows "
                                f"({rows_per_sec:.0f} rows/sec), memory: {current_memory_mb:.1f} MB"
                            )

                        # Allow other tasks to run
                        await asyncio.sleep(0)

                # Save Excel file
                saved_file_path = excel_writer.save()

                elapsed = time.time() - start_time
                log.info(
                    f"Export {export_id}: Completed data processing - {total_rows} rows in {elapsed:.2f}s"
                )

                # Signal that data processing is complete
                await queue.put(("data_complete", saved_file_path))

            except Exception as e:
                log.error(
                    f"Export {export_id}: Error in data processing: {e}", exc_info=True
                )
                await queue.put(("error", str(e)))
                # Note: Excel writer cleanup moved to background task to prevent premature file deletion

        except Exception as e:
            log.error(
                f"Export {export_id}: Fatal error in streaming processing: {e}",
                exc_info=True,
            )
            await queue.put(("error", str(e)))

    # Background task to stream file content
    async def stream_file_content():
        try:
            # Wait for data processing to complete
            while True:
                message_type, data = await queue.get()

                if message_type == "data_complete":
                    temp_file_path = data
                    break
                elif message_type == "error":
                    raise Exception(f"Data processing failed: {data}")

            # Stream the Excel file
            log.info(f"Export {export_id}: Starting file streaming")

            file_size = os.path.getsize(temp_file_path)
            position = 0

            with open(temp_file_path, "rb") as f:
                # Check if file starts with ZIP signature (Excel files are ZIP archives)
                first_bytes = f.read(4)
                f.seek(0)

                if first_bytes != b"PK\x03\x04":
                    log.warning(
                        f"Export {export_id}: Excel file doesn't start with ZIP signature"
                    )

                while position < file_size:
                    file_chunk = f.read(OPTIMAL_CHUNK_SIZE)
                    if not file_chunk:
                        break
                    position += len(file_chunk)
                    await queue.put(("file_chunk", file_chunk))

            await queue.put(("file_complete", None))
            log.info(f"Export {export_id}: File streaming completed")

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in file streaming: {e}", exc_info=True
            )
            await queue.put(("error", str(e)))

    # Start background tasks
    data_task = asyncio.create_task(process_data_streaming())
    stream_task = asyncio.create_task(stream_file_content())

    # Generator function for streaming response
    async def stream_generator():
        try:
            while True:
                message_type, data = await queue.get()

                if message_type == "file_chunk":
                    yield data
                elif message_type == "file_complete":
                    break
                elif message_type == "error":
                    log.error(f"Export {export_id}: Stream error: {data}")
                    yield f"Error: {data}".encode("utf-8")
                    break

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in stream generator: {e}", exc_info=True
            )
            yield f"Error: {str(e)}".encode("utf-8")
        finally:
            # Clean up tasks
            if not data_task.done():
                data_task.cancel()
            if not stream_task.done():
                stream_task.cancel()

    # Cleanup function
    async def cleanup_export():
        try:
            # Wait for tasks to complete or cancel them
            await asyncio.gather(data_task, stream_task, return_exceptions=True)

            # Clean up Excel writer temp files
            if excel_writer_for_cleanup:
                try:
                    excel_writer_for_cleanup.cleanup()
                    log.info(f"Export {export_id}: Cleaned up Excel temp files")
                except Exception as cleanup_error:
                    log.error(
                        f"Export {export_id}: Error cleaning up Excel temp files: {cleanup_error}",
                        exc_info=True,
                    )

            # Clean up source file if specified
            if cleanup_source_file_path:
                try:
                    from magic_gateway.utils.parquet_processor import (
                        cleanup_parquet_file,
                    )

                    cleanup_parquet_file(cleanup_source_file_path)
                    log.info(
                        f"Export {export_id}: Cleaned up source file: {cleanup_source_file_path}"
                    )
                except Exception as cleanup_error:
                    log.error(
                        f"Export {export_id}: Error cleaning up source file: {cleanup_error}",
                        exc_info=True,
                    )
        except Exception as e:
            log.error(f"Export {export_id}: Error in cleanup: {e}", exc_info=True)

    # Return streaming response
    return EnhancedStreamingResponse(
        content_generator=stream_generator(),
        filename=filename,
        background=BackgroundTask(cleanup_export),
    )


async def generate_non_blocking_excel_response(
    data_iterator: AsyncIterable[List[Dict[str, Any]]],
    filename: str,
    job_info: Optional[Any] = None,
    chunk_size: int = OPTIMAL_CHUNK_SIZE,
    horizontal_facts: bool = False,
) -> StreamingResponse:
    """
    Generate an Excel response without blocking the worker.

    This function uses a combination of techniques to avoid blocking:
    1. Initial bytes are sent immediately to start the download
    2. Data processing continues in the background
    3. Excel generation is offloaded to a thread pool
    4. The worker is freed up quickly to handle other requests

    Args:
        data_iterator: Async iterable yielding chunks of data
        filename: Name of the file
        job_info: Optional job information to include
        chunk_size: Size of chunks for streaming
        horizontal_facts: Whether to use horizontal facts format for Excel

    Returns:
        StreamingResponse that starts downloading immediately
    """
    # Generate a unique ID for this export
    export_id = str(uuid.uuid4())

    # Create a queue for communication between tasks
    queue = asyncio.Queue()

    # Store data chunks with memory management
    all_data_chunks = []

    # Background task to collect data without blocking
    async def collect_data():
        nonlocal all_data_chunks
        try:
            log.info(
                f"Starting memory-optimized data collection for export {export_id}"
            )
            start_time = time.time()
            total_rows = 0

            # Process data iterator with memory limits
            async for chunk in data_iterator:
                if chunk:
                    chunk_size = len(chunk)

                    # Memory management: limit the number of chunks in memory
                    if len(all_data_chunks) >= MAX_MEMORY_CHUNKS:
                        log.info(
                            f"Export {export_id}: Memory threshold reached ({len(all_data_chunks)} chunks), "
                            f"will process in batches during Excel generation"
                        )
                        # Continue collecting but log the memory pressure

                    all_data_chunks.append(chunk)
                    total_rows += chunk_size

                    # Update progress periodically
                    if total_rows % 100000 == 0:
                        elapsed = time.time() - start_time
                        rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                        current_memory_mb = get_memory_usage_mb()
                        log.info(
                            f"Export {export_id}: Collected {total_rows} rows "
                            f"({rows_per_sec:.1f} rows/sec, {len(all_data_chunks)} chunks in memory, memory: {current_memory_mb:.1f}MB)"
                        )
                        await queue.put(("data_progress", total_rows))

                # Yield control back to the event loop
                await asyncio.sleep(0)

            # Signal that data collection is complete
            elapsed = time.time() - start_time
            log.info(
                f"Export {export_id}: Data collection complete - {total_rows} rows in {elapsed:.2f}s"
            )
            await queue.put(("data_complete", total_rows))

            # Generate Excel file in thread pool
            log.info(
                f"Export {export_id}: Starting Excel generation in thread pool (horizontal_facts={horizontal_facts})"
            )
            temp_file_path = await asyncio.get_event_loop().run_in_executor(
                excel_thread_pool,
                create_excel_file_in_thread,
                all_data_chunks,
                filename,
                job_info,
                horizontal_facts,
            )

            # Signal that Excel generation is complete
            await queue.put(("excel_complete", temp_file_path))
            log.info(
                f"Export {export_id}: Excel generation complete - {temp_file_path}"
            )

            # Stream the file
            file_size = os.path.getsize(temp_file_path)
            log.info(
                f"Export {export_id}: Starting to stream Excel file of size {file_size / 1024 / 1024:.2f}MB"
            )
            with open(temp_file_path, "rb") as f:
                # Check if file starts with ZIP signature (which Excel files should)
                first_bytes = f.read(4)
                if first_bytes == b"PK\x03\x04":
                    # Skip the first 4 bytes (ZIP signature) since we've already sent them
                    log.info(
                        f"Export {export_id}: Skipping initial ZIP signature as it was already sent"
                    )
                    position = 4
                else:
                    # If not, reset to beginning and send everything
                    f.seek(0)
                    position = 0
                    log.warning(
                        f"Export {export_id}: Excel file doesn't start with ZIP signature"
                    )

                while position < file_size:
                    file_chunk = f.read(OPTIMAL_CHUNK_SIZE)
                    if not file_chunk:
                        break
                    position += len(file_chunk)
                    await queue.put(("file_chunk", file_chunk))

                    # Log progress for large files
                    if position % (10 * 1024 * 1024) == 0:  # Every 10MB
                        log.info(
                            f"Export {export_id}: Streaming progress - "
                            f"{position / 1024 / 1024:.1f}MB / {file_size / 1024 / 1024:.1f}MB"
                        )

            # Signal that streaming is complete
            await queue.put(("stream_complete", None))
            log.info(f"Export {export_id}: File streaming complete")

            # Clean up the temporary file
            try:
                os.unlink(temp_file_path)
                log.info(f"Export {export_id}: Temporary file removed")
            except Exception as e:
                log.warning(f"Export {export_id}: Error removing temporary file: {e}")

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in collect_data task: {e}", exc_info=True
            )
            await queue.put(("error", str(e)))

    # Start the data collection task
    active_exports[export_id] = asyncio.create_task(collect_data())

    # Streaming generator that yields data as it becomes available
    async def stream_generator():
        try:
            # Send initial bytes to start download immediately
            # This is the ZIP file signature that all Excel files start with
            yield b"PK\x03\x04"  # ZIP file signature
            log.info(
                f"Export {export_id}: Initial ZIP signature sent to start download"
            )

            # Process queue messages
            while True:
                # Check for new messages with a short timeout
                try:
                    message_type, message_data = await asyncio.wait_for(
                        queue.get(), timeout=0.5
                    )

                    if message_type == "file_chunk":
                        # Stream file chunk
                        chunk_size = len(message_data) if message_data else 0
                        if chunk_size > 0:
                            log.debug(
                                f"Export {export_id}: Streaming chunk of {chunk_size} bytes"
                            )
                        yield message_data

                    elif message_type == "stream_complete":
                        # Streaming is complete
                        log.info(f"Export {export_id}: Streaming complete")
                        break

                    elif message_type == "error":
                        # An error occurred
                        log.error(
                            f"Export {export_id}: Error during export: {message_data}"
                        )
                        error_message = f"Error generating Excel file: {message_data}"
                        yield error_message.encode("utf-8")
                        break

                except asyncio.TimeoutError:
                    # No new messages, continue waiting
                    continue

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in stream_generator: {e}", exc_info=True
            )
            error_message = f"Error streaming Excel file: {str(e)}"
            yield error_message.encode("utf-8")

        finally:
            # Clean up the export task if it's still running
            if export_id in active_exports:
                task = active_exports.pop(export_id)
                if not task.done():
                    log.info(f"Export {export_id}: Cancelling background task")
                    task.cancel()

    # Create a background task to clean up resources
    def cleanup_export():
        if export_id in active_exports:
            task = active_exports.pop(export_id)
            if not task.done():
                log.info(
                    f"Export {export_id}: Cancelling background task during cleanup"
                )
                task.cancel()

    # Return streaming response
    return EnhancedStreamingResponse(
        content_generator=stream_generator(),
        filename=filename,
        background=BackgroundTask(cleanup_export),
    )


async def generate_intelligent_export_response(
    parquet_file_path: str,
    filename: str,
    requested_format: str,
    job_info: Optional[Any] = None,
    allow_format_change: bool = True,
    horizontal_facts: bool = False,
    cleanup_source_file: bool = False,
    separate_periods: Optional[bool] = None,
) -> StreamingResponse:
    """
    Generate an export response using intelligent format selection.

    This function analyzes the data size and automatically selects the optimal
    export format and method based on Excel row limits and period splitting viability.

    Args:
        parquet_file_path: Path to the Parquet file containing the data
        filename: Name for the exported file
        requested_format: Originally requested format ('excel', 'excel_facts', 'csv', 'parquet')
        job_info: Optional job information to include
        allow_format_change: Whether to allow automatic format changes for large datasets
        horizontal_facts: Whether to use horizontal facts format (for Excel formats)
        cleanup_source_file: Whether to clean up the source Parquet file after streaming
        separate_periods: Explicit user preference for period separation (None=auto, True=force, False=disable)

    Returns:
        StreamingResponse with the optimal export format
    """
    try:
        log.info(
            f"Starting intelligent export for {filename} (requested: {requested_format})"
        )

        # Determine the optimal export format
        format_decision = await determine_optimal_export_format(
            parquet_file_path=parquet_file_path,
            requested_format=requested_format,
            allow_format_change=allow_format_change,
            job_info=job_info,
            separate_periods=separate_periods,
        )

        # Log detailed decision information
        log_export_decision_details(format_decision, filename)

        # Generate user-friendly feedback message
        user_message = generate_user_feedback_message(format_decision)

        # Log the user-friendly message
        if format_decision.format_changed:
            log.warning(f"Export format decision for {filename}:")
            for line in user_message.split("\n"):
                if line.strip():
                    log.warning(f"  {line}")
        else:
            log.info(f"Export format decision for {filename}: {user_message}")

        # Execute the appropriate export based on the decision
        if format_decision.recommended_format.lower() == "csv":
            # CSV export
            log.info(f"Generating CSV export for {filename}")

            # Create async data iterator from Parquet file using streaming approach
            async def csv_data_iterator():
                async for chunk in read_parquet_file_async_streaming(parquet_file_path):
                    if chunk:
                        yield chunk

            return await generate_streaming_csv_response(
                data_chunks=csv_data_iterator(), filename=filename
            )

        elif format_decision.recommended_format.lower() == "parquet":
            # Parquet export (direct file response)
            log.info(f"Generating Parquet export for {filename}")
            from magic_gateway.utils.parquet_processor import (
                create_parquet_file_response,
            )

            return create_parquet_file_response(
                file_path=parquet_file_path,
                filename=filename,
                cleanup_file=cleanup_source_file,  # Use the cleanup parameter
            )

        elif format_decision.recommended_format.lower() in ["excel", "excel_facts"]:
            # Excel export (with or without period splitting)
            horizontal_facts_mode = (
                format_decision.recommended_format.lower() == "excel_facts"
                or horizontal_facts
            )

            if format_decision.use_period_split:
                # Use period-separated Excel export
                log.info(f"Generating period-separated Excel export for {filename}")

                # Use streaming period-separated Excel export for better memory efficiency
                return await generate_period_separated_excel_response_streaming(
                    parquet_file_path=parquet_file_path,
                    filename=filename,
                    job_info=job_info,
                    horizontal_facts=horizontal_facts_mode,
                    cleanup_source_file=cleanup_source_file,
                )
            else:
                # Use standard Excel export
                log.info(f"Generating standard Excel export for {filename}")

                # Create async data iterator from Parquet file using streaming approach
                async def excel_data_iterator():
                    async for chunk in read_parquet_file_async_streaming(
                        parquet_file_path
                    ):
                        if chunk:
                            yield chunk

                return await generate_streaming_excel_response(
                    data_iterator=excel_data_iterator(),
                    filename=filename,
                    job_info=job_info,
                    horizontal_facts=horizontal_facts_mode,
                    cleanup_source_file_path=parquet_file_path
                    if cleanup_source_file
                    else None,
                )
        else:
            # Fallback to CSV if format is not recognized
            log.warning(
                f"Unrecognized format {format_decision.recommended_format}, falling back to CSV"
            )

            async def fallback_data_iterator():
                async for chunk in read_parquet_file_async_streaming(parquet_file_path):
                    if chunk:
                        yield chunk

            return await generate_streaming_csv_response(
                data_chunks=fallback_data_iterator(), filename=filename
            )

    except Exception as e:
        log.error(f"Error in intelligent export generation: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to generate export: {str(e)}"
        )


def _create_period_separated_excel_file(
    period_groups: Dict[str, List[List[Dict[str, Any]]]],
    temp_file_path: str,
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
) -> str:
    """
    Create an Excel file with separate sheets for each period.

    Args:
        period_groups: Dictionary mapping period values to lists of data chunks
        temp_file_path: Path where the Excel file should be created
        filename: Name for the Excel file
        job_info: Optional job information to include
        horizontal_facts: Whether to use horizontal facts format

    Returns:
        Path to the created Excel file
    """
    try:
        start_time = time.time()
        log.info(f"Creating period-separated Excel file: {filename}")

        import xlsxwriter

        # Create Excel workbook
        workbook = xlsxwriter.Workbook(temp_file_path, {"nan_inf_to_errors": True})

        # Create formats
        header_format = workbook.add_format(
            {"bold": True, "bg_color": "#D7E4BC", "border": 1}
        )

        # Process each period as a separate sheet
        for period_value, period_chunks in period_groups.items():
            # Sanitize sheet name (Excel has restrictions)
            sheet_name = str(period_value)[:31]  # Excel sheet names max 31 chars
            sheet_name = (
                sheet_name.replace("/", "_").replace("\\", "_").replace(":", "_")
            )

            log.info(f"Creating sheet '{sheet_name}' with {len(period_chunks)} chunks")

            # Create worksheet
            worksheet = workbook.add_worksheet(sheet_name)

            # Initialize variables outside conditional blocks to fix scope error
            current_row = 0
            total_rows_written = 0
            headers_written = False

            # Handle horizontal facts format if requested
            if horizontal_facts:
                log.debug(f"Processing horizontal facts for sheet '{sheet_name}'")
                try:
                    # Process horizontal facts transformation manually for better control
                    fact_col = None
                    value_col = None
                    index_cols = []
                    unique_facts = set()
                    fact_values = {}

                    # First pass: identify columns and collect unique facts
                    for chunk in period_chunks:
                        if not chunk:
                            continue

                        # Identify fact and value columns from first row
                        if fact_col is None and chunk:
                            first_row = chunk[0]

                            # Look for fact column
                            fact_keywords = [
                                "fact",
                                "metric",
                                "measure",
                                "indicator",
                                "kpi",
                            ]
                            for col in first_row.keys():
                                if any(
                                    keyword in col.lower() for keyword in fact_keywords
                                ):
                                    fact_col = col
                                    break

                            # Look for value column
                            value_keywords = ["value", "val", "amount", "result"]
                            for col in first_row.keys():
                                if any(
                                    keyword in col.lower() for keyword in value_keywords
                                ):
                                    value_col = col
                                    break

                            # Index columns are all others
                            if fact_col and value_col:
                                index_cols = [
                                    col
                                    for col in first_row.keys()
                                    if col != fact_col and col != value_col
                                ]
                                log.debug(
                                    f"Sheet '{sheet_name}': fact='{fact_col}', value='{value_col}', index_cols={index_cols}"
                                )
                            else:
                                log.warning(
                                    f"Sheet '{sheet_name}': Could not identify fact/value columns, falling back to standard format"
                                )
                                horizontal_facts = False
                                break

                        # Collect unique facts and build fact_values dictionary
                        if fact_col and value_col:
                            for row in chunk:
                                fact = str(row.get(fact_col, ""))
                                value = row.get(value_col, "")

                                if fact:
                                    unique_facts.add(fact)

                                    # Create index key from index columns
                                    index_key = tuple(
                                        str(row.get(col, "")) for col in index_cols
                                    )

                                    if index_key not in fact_values:
                                        fact_values[index_key] = {}

                                    fact_values[index_key][fact] = value

                    # Write horizontal facts data to worksheet
                    if horizontal_facts and fact_col and value_col and fact_values:
                        # Write headers
                        headers = index_cols + sorted(list(unique_facts))
                        for col, header in enumerate(headers):
                            worksheet.write(current_row, col, header, header_format)
                        current_row += 1
                        headers_written = True

                        # Write data rows
                        for index_key, fact_dict in fact_values.items():
                            # Write index columns
                            for col, value in enumerate(index_key):
                                worksheet.write(current_row, col, value)

                            # Write fact values
                            for col, fact in enumerate(
                                sorted(list(unique_facts)), start=len(index_cols)
                            ):
                                value = fact_dict.get(fact, "")
                                worksheet.write(current_row, col, value)

                            current_row += 1
                            total_rows_written += 1

                        log.debug(
                            f"Sheet '{sheet_name}': wrote horizontal facts with {total_rows_written} rows, {len(unique_facts)} facts"
                        )
                    else:
                        log.warning(
                            f"Sheet '{sheet_name}': no horizontal facts data generated, falling back to standard format"
                        )
                        horizontal_facts = False

                except Exception as hf_error:
                    log.error(
                        f"Error creating horizontal facts for sheet '{sheet_name}': {hf_error}",
                        exc_info=True,
                    )
                    # Fallback to standard format
                    horizontal_facts = False

            if not horizontal_facts:
                # Write data directly to the worksheet (standard format)
                # Reset variables for standard format (they may have been used in horizontal facts)
                current_row = 0
                headers_written = False
                total_rows_written = 0

                # Process all chunks for this period
                for chunk in period_chunks:
                    if not chunk:
                        continue

                    # Write headers from first chunk
                    if not headers_written and chunk:
                        headers = list(chunk[0].keys())
                        for col, header in enumerate(headers):
                            worksheet.write(current_row, col, header, header_format)
                        current_row += 1
                        headers_written = True
                        log.debug(f"Sheet '{sheet_name}': wrote {len(headers)} headers")

                    # Write data rows
                    for row_data in chunk:
                        for col, header in enumerate(headers):
                            value = clean_excel_value(row_data.get(header, ""))
                            worksheet.write(current_row, col, value)
                        current_row += 1
                        total_rows_written += 1

                # Set column widths for better readability
                if headers_written:
                    for col in range(len(headers)):
                        worksheet.set_column(col, col, 15)

                log.debug(
                    f"Sheet '{sheet_name}': wrote standard format with {total_rows_written} rows"
                )

            log.debug(
                f"Sheet '{sheet_name}' completed with {total_rows_written} data rows"
            )

        # Add job info sheet if provided
        if job_info:
            try:
                # Use the unified info sheet generator
                info_generator = InfoSheetGenerator(workbook)
                info_generator.create_info_sheet(job_info, filename)
                log.debug("Info sheet created using unified generator")

            except Exception as info_error:
                log.warning(
                    f"Failed to add job info sheet: {info_error}", exc_info=True
                )

        # Close workbook
        workbook.close()

        elapsed_time = time.time() - start_time
        file_size = os.path.getsize(temp_file_path)
        log.info(
            f"Period-separated Excel file created in {elapsed_time:.2f}s: "
            f"{len(period_groups)} sheets, {file_size} bytes"
        )

        return temp_file_path

    except Exception as e:
        log.error(f"Error creating period-separated Excel file: {e}", exc_info=True)
        raise


async def generate_period_separated_excel_response_streaming(
    parquet_file_path: str,
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
    cleanup_source_file: bool = False,
) -> StreamingResponse:
    """
    Generate an Excel response with separate sheets for each period using streaming approach.

    This function processes the Parquet file in a streaming fashion and creates period-separated
    Excel sheets without loading all data into memory at once.

    Args:
        parquet_file_path: Path to the Parquet file to process
        filename: Name for the Excel file
        job_info: Optional job information to include
        horizontal_facts: Whether to use horizontal facts format
        cleanup_source_file: Whether to clean up the source Parquet file after streaming

    Returns:
        StreamingResponse with the Excel file
    """
    # Generate a unique ID for this export
    export_id = str(uuid.uuid4())

    log.info(
        f"Starting streaming period-separated Excel export {export_id} for {filename}"
    )

    # Create a queue for communication between tasks
    queue = asyncio.Queue()

    # Variable to store excel_writer for cleanup
    excel_writer_for_cleanup = None

    # Background task to process data and generate Excel file with period separation
    async def process_period_separated_data():
        try:
            log.info(
                f"Starting streaming period-separated data processing for export {export_id}"
            )
            start_time = time.time()

            # Create streaming Excel writer
            excel_writer = StreamingExcelWriter(
                filename, horizontal_facts=horizontal_facts
            )

            # Store reference for cleanup
            nonlocal excel_writer_for_cleanup
            excel_writer_for_cleanup = excel_writer

            try:
                # Add info sheet if job info is provided
                if job_info:
                    excel_writer.add_info_sheet(job_info)

                # Process data in streaming fashion and separate by periods
                period_sheets = {}  # Track sheets for each period
                total_rows = 0
                period_column = None

                async for chunk in read_parquet_file_async_streaming(parquet_file_path):
                    if not chunk:
                        continue

                    # Identify period column from first chunk
                    if period_column is None:
                        headers = list(chunk[0].keys()) if chunk else []
                        period_column = identify_period_column(headers)

                        if not period_column:
                            log.warning(
                                "No period column identified, using single sheet"
                            )
                            period_column = None
                        else:
                            log.info(f"Identified period column: {period_column}")

                    # Group chunk data by periods
                    period_chunk_data = {}
                    for row in chunk:
                        period_value = (
                            row.get(period_column) if period_column else "All Data"
                        )
                        period_key = (
                            str(period_value)
                            if period_value is not None
                            else "All Data"
                        )

                        if period_key not in period_chunk_data:
                            period_chunk_data[period_key] = []
                        period_chunk_data[period_key].append(row)

                    # Write data to appropriate sheets
                    for period_key, period_rows in period_chunk_data.items():
                        if period_rows:  # Only process non-empty period data
                            # For now, write to main sheet (StreamingExcelWriter handles period separation)
                            excel_writer.write_data_chunk(period_rows)
                            total_rows += len(period_rows)

                    # Log progress periodically
                    if total_rows % 100000 == 0:
                        elapsed = time.time() - start_time
                        rows_per_sec = total_rows / elapsed if elapsed > 0 else 0
                        current_memory_mb = get_memory_usage_mb()
                        log.info(
                            f"Export {export_id}: Processed {total_rows} rows "
                            f"({rows_per_sec:.0f} rows/sec), memory: {current_memory_mb:.1f} MB"
                        )

                    # Allow other tasks to run
                    await asyncio.sleep(0)

                # Save Excel file
                saved_file_path = excel_writer.save()

                elapsed = time.time() - start_time
                log.info(
                    f"Export {export_id}: Completed period-separated processing - {total_rows} rows in {elapsed:.2f}s"
                )

                # Signal that data processing is complete
                await queue.put(("data_complete", saved_file_path))

            except Exception as e:
                log.error(
                    f"Export {export_id}: Error in period-separated processing: {e}",
                    exc_info=True,
                )
                await queue.put(("error", str(e)))
                # Note: Excel writer cleanup moved to background task to prevent premature file deletion

        except Exception as e:
            log.error(
                f"Export {export_id}: Fatal error in streaming period-separated processing: {e}",
                exc_info=True,
            )
            await queue.put(("error", str(e)))

    # Background task to stream file content (reuse from streaming Excel response)
    async def stream_file_content():
        try:
            # Wait for data processing to complete
            while True:
                message_type, data = await queue.get()

                if message_type == "data_complete":
                    temp_file_path = data
                    break
                elif message_type == "error":
                    raise Exception(f"Data processing failed: {data}")

            # Stream the Excel file
            log.info(f"Export {export_id}: Starting file streaming")

            file_size = os.path.getsize(temp_file_path)
            position = 0

            with open(temp_file_path, "rb") as f:
                while position < file_size:
                    file_chunk = f.read(OPTIMAL_CHUNK_SIZE)
                    if not file_chunk:
                        break
                    position += len(file_chunk)
                    await queue.put(("file_chunk", file_chunk))

            await queue.put(("file_complete", None))
            log.info(f"Export {export_id}: File streaming completed")

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in file streaming: {e}", exc_info=True
            )
            await queue.put(("error", str(e)))

    # Start background tasks
    data_task = asyncio.create_task(process_period_separated_data())
    stream_task = asyncio.create_task(stream_file_content())

    # Generator function for streaming response
    async def stream_generator():
        try:
            while True:
                message_type, data = await queue.get()

                if message_type == "file_chunk":
                    yield data
                elif message_type == "file_complete":
                    break
                elif message_type == "error":
                    log.error(f"Export {export_id}: Stream error: {data}")
                    yield f"Error: {data}".encode("utf-8")
                    break

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in stream generator: {e}", exc_info=True
            )
            yield f"Error: {str(e)}".encode("utf-8")
        finally:
            # Clean up tasks
            if not data_task.done():
                data_task.cancel()
            if not stream_task.done():
                stream_task.cancel()

    # Cleanup function
    async def cleanup_export():
        try:
            # Wait for tasks to complete or cancel them
            await asyncio.gather(data_task, stream_task, return_exceptions=True)

            # Clean up Excel writer temp files
            if excel_writer_for_cleanup:
                try:
                    excel_writer_for_cleanup.cleanup()
                    log.info(f"Export {export_id}: Cleaned up Excel temp files")
                except Exception as cleanup_error:
                    log.error(
                        f"Export {export_id}: Error cleaning up Excel temp files: {cleanup_error}",
                        exc_info=True,
                    )

            # Clean up source Parquet file if requested
            if cleanup_source_file:
                try:
                    from magic_gateway.utils.parquet_processor import (
                        cleanup_parquet_file,
                    )

                    cleanup_parquet_file(parquet_file_path)
                    log.info(
                        f"Export {export_id}: Cleaned up source Parquet file: {parquet_file_path}"
                    )
                except Exception as cleanup_error:
                    log.error(
                        f"Export {export_id}: Error cleaning up source file: {cleanup_error}",
                        exc_info=True,
                    )
        except Exception as e:
            log.error(f"Export {export_id}: Error in cleanup: {e}", exc_info=True)

    # Return streaming response
    return EnhancedStreamingResponse(
        content_generator=stream_generator(),
        filename=filename,
        background=BackgroundTask(cleanup_export),
    )


async def generate_period_separated_excel_response(
    period_groups: Dict[str, List[List[Dict[str, Any]]]],
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
) -> StreamingResponse:
    """
    Legacy function: Generate an Excel response with separate sheets for each period.

    WARNING: This function may use significant memory for large datasets as it requires
    all period groups to be loaded into memory. Use generate_period_separated_excel_response_streaming()
    for memory-efficient processing.

    Args:
        period_groups: Dictionary mapping period values to lists of data chunks
        filename: Name for the Excel file
        job_info: Optional job information to include
        horizontal_facts: Whether to use horizontal facts format

    Returns:
        StreamingResponse with the Excel file
    """
    temp_file_path = None

    try:
        log.info(f"Starting period-separated Excel generation for {filename}")
        log.info(f"Creating Excel with {len(period_groups)} period sheets")

        # Create temporary file for Excel
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
        temp_file_path = temp_file.name
        temp_file.close()

        # Generate Excel file in thread pool
        def create_period_separated_excel():
            return _create_period_separated_excel_file(
                period_groups, temp_file_path, filename, job_info, horizontal_facts
            )

        # Execute in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(excel_thread_pool, create_period_separated_excel)

        # Verify file was created
        if not os.path.exists(temp_file_path):
            raise Exception("Excel file was not created")

        file_size = os.path.getsize(temp_file_path)
        log.info(f"Period-separated Excel file created: {file_size} bytes")

        # Create cleanup function
        def cleanup_temp_file():
            try:
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                    log.debug(f"Cleaned up temporary Excel file: {temp_file_path}")
            except Exception as cleanup_error:
                log.warning(f"Failed to clean up temp file: {cleanup_error}")

        # Create streaming response
        def file_generator():
            try:
                with open(temp_file_path, "rb") as f:
                    while True:
                        chunk = f.read(8192)  # 8KB chunks
                        if not chunk:
                            break
                        yield chunk
            finally:
                cleanup_temp_file()

        response = StreamingResponse(
            file_generator(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}.xlsx"},
        )

        log.info(f"Period-separated Excel response created for {filename}")
        return response

    except Exception as e:
        log.error(f"Error in period-separated Excel generation: {e}", exc_info=True)
        # Clean up temp file if it exists
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.remove(temp_file_path)
            except:
                pass
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate period-separated Excel file: {str(e)}",
        )


def extract_period_labels_from_metadata(job_info: Any) -> List[str]:
    """
    Extract period labels from job_info metadata for ClickHouse filtering.

    This function parses the job_info metadata to find period information
    and extracts the period labels that can be used for filtering ClickHouse data.

    Expected period format in metadata:
    [Period(label='Q1 2023', date_start='2023-01-01', date_end='2023-03-31', total_positions=144, split_axis=None, threshold=0)]

    Args:
        job_info: Job information containing period metadata

    Returns:
        List of period labels extracted from the metadata
    """
    import re

    period_labels = []

    try:
        # Process job_info to get structured data
        from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator

        info_generator = InfoSheetGenerator(None)  # No workbook needed for processing
        processed_info = info_generator.process_job_info(job_info, "period_extraction")

        # Look for period information in various fields
        period_sources = [
            processed_info.get("periods", ""),
            processed_info.get("info", ""),
            processed_info.get("job_info", ""),
        ]

        # Also check nested job_info
        if "job_info" in processed_info:
            try:
                nested_job_info = processed_info["job_info"]
                if isinstance(nested_job_info, str):
                    nested_data = json.loads(nested_job_info)
                elif isinstance(nested_job_info, dict):
                    nested_data = nested_job_info
                else:
                    nested_data = {}

                if isinstance(nested_data, dict):
                    period_sources.extend(
                        [
                            nested_data.get("periods", ""),
                            nested_data.get("info", ""),
                        ]
                    )
            except (json.JSONDecodeError, TypeError, AttributeError):
                log.debug("Could not parse nested job_info for period extraction")

        # Extract period labels from each source
        for source in period_sources:
            if not source:
                continue

            source_str = str(source)

            # Pattern to match Period objects with label parameter
            # Matches: Period(label='Q1 2023', ...) or Period(label="Q1 2023", ...)
            period_pattern = r"Period\([^)]*label=['\"]([^'\"]+)['\"][^)]*\)"
            matches = re.findall(period_pattern, source_str)

            for match in matches:
                if match and match not in period_labels:
                    period_labels.append(match)

            # Also try to parse as JSON array if it looks like one
            if source_str.strip().startswith("[") and source_str.strip().endswith("]"):
                try:
                    parsed_list = json.loads(source_str)
                    if isinstance(parsed_list, list):
                        for item in parsed_list:
                            if isinstance(item, dict) and "label" in item:
                                label = item["label"]
                                if label and label not in period_labels:
                                    period_labels.append(label)
                            elif isinstance(item, str):
                                # Try to extract label from string representation
                                label_match = re.search(
                                    r"label=['\"]([^'\"]+)['\"]", item
                                )
                                if label_match:
                                    label = label_match.group(1)
                                    if label and label not in period_labels:
                                        period_labels.append(label)
                except json.JSONDecodeError:
                    pass

        log.info(
            f"Extracted {len(period_labels)} period labels from metadata: {period_labels}"
        )
        return period_labels

    except Exception as e:
        log.error(f"Error extracting period labels from metadata: {e}", exc_info=True)
        return []


async def query_clickhouse_by_period(
    table_name: str, period_label: str, period_column: str, query_id: str
) -> Tuple[str, int]:
    """
    Query ClickHouse data for a specific period and export to Parquet.

    Args:
        table_name: Name of the ClickHouse table to query
        period_label: Period label to filter by
        period_column: Name of the period column in the table
        query_id: Unique query ID for tracking

    Returns:
        Tuple of (parquet_file_path, row_count)
    """
    try:
        # Build the query with period filtering
        query = f"""
            SELECT *
            FROM {table_name}
            WHERE {period_column} = '{period_label}'
        """

        log.info(
            f"Querying ClickHouse for period '{period_label}' with query ID {query_id}"
        )

        # Export the filtered data to a temporary Parquet file
        temp_parquet_path, row_count = await ClickHouseHandler.export_to_parquet_native(
            query=query,
            output_file_path=f"/tmp/period_{period_label}_{query_id}.parquet",
            query_id=query_id,
            allow_write=False,
        )

        log.info(
            f"Exported {row_count} rows for period '{period_label}' to {temp_parquet_path}"
        )
        return temp_parquet_path, row_count

    except Exception as e:
        log.error(
            f"Error querying ClickHouse for period '{period_label}': {e}", exc_info=True
        )
        raise


async def generate_period_separated_excel_from_clickhouse(
    table_name: str,
    filename: str,
    job_info: Optional[Any] = None,
    horizontal_facts: bool = False,
    cleanup_source_files: bool = True,
) -> StreamingResponse:
    """
    Generate an Excel response with separate sheets for each period by querying ClickHouse directly.

    This function implements the new workflow:
    1. Extract period labels from metadata
    2. Query ClickHouse for each period separately
    3. Stream each period's data to separate Excel sheets

    Args:
        table_name: Name of the ClickHouse table to query
        filename: Name for the Excel file
        job_info: Optional job information containing period metadata
        horizontal_facts: Whether to use horizontal facts format
        cleanup_source_files: Whether to clean up temporary Parquet files

    Returns:
        StreamingResponse with the Excel file
    """
    # Generate a unique ID for this export
    export_id = str(uuid.uuid4())

    log.info(
        f"Starting period-separated Excel export from ClickHouse {export_id} for {filename}"
    )

    # Extract period labels from metadata
    period_labels = extract_period_labels_from_metadata(job_info) if job_info else []

    if not period_labels:
        log.warning(
            "No period labels found in metadata, falling back to standard export"
        )
        # Fall back to standard export without period separation
        raise HTTPException(
            status_code=400,
            detail="No period labels found in metadata for period separation",
        )

    log.info(f"Found {len(period_labels)} periods to export: {period_labels}")

    # Create a queue for communication between tasks
    queue = asyncio.Queue()

    # Variable to store excel_writer for cleanup
    excel_writer_for_cleanup = None

    # List to track temporary files for cleanup
    temp_files = []

    # Background task to process periods and generate Excel file
    async def process_periods_and_generate_excel():
        try:
            log.info(f"Starting period-based data processing for export {export_id}")
            start_time = time.time()

            # Create streaming Excel writer
            excel_writer = StreamingExcelWriter(
                filename, horizontal_facts=horizontal_facts
            )

            # Store reference for cleanup
            nonlocal excel_writer_for_cleanup
            excel_writer_for_cleanup = excel_writer

            try:
                # Add info sheet if job info is provided
                if job_info:
                    excel_writer.add_info_sheet(job_info)

                # Identify period column from the first period's data
                period_column = None
                total_rows = 0

                # Process each period separately
                for i, period_label in enumerate(period_labels):
                    log.info(
                        f"Processing period {i + 1}/{len(period_labels)}: '{period_label}'"
                    )

                    # Generate unique query ID for this period
                    period_query_id = (
                        f"{export_id}_period_{i}_{period_label.replace(' ', '_')}"
                    )

                    try:
                        # First, we need to identify the period column if not already done
                        if period_column is None:
                            # Query a small sample to identify the period column
                            sample_query = f"SELECT * FROM {table_name} LIMIT 10"
                            sample_rows, _ = await ClickHouseHandler.execute_query(
                                query=sample_query,
                                query_id=f"{export_id}_sample",
                                allow_write=False,
                            )

                            if sample_rows:
                                headers = (
                                    list(sample_rows[0].keys()) if sample_rows else []
                                )
                                period_column = identify_period_column(headers)

                                if not period_column:
                                    log.warning(
                                        "No period column identified in data, using first column"
                                    )
                                    period_column = headers[0] if headers else "period"

                                log.info(f"Identified period column: {period_column}")
                            else:
                                # Fallback if no sample data
                                period_column = "period"
                                log.warning(
                                    "No sample data found, using default period column name"
                                )

                        # Query ClickHouse for this specific period
                        (
                            period_parquet_path,
                            period_row_count,
                        ) = await query_clickhouse_by_period(
                            table_name=table_name,
                            period_label=period_label,
                            period_column=period_column,
                            query_id=period_query_id,
                        )

                        # Track temp file for cleanup
                        temp_files.append(period_parquet_path)

                        if period_row_count > 0:
                            # Stream data from this period's Parquet file to Excel
                            # Use the new period-specific method to create separate sheets
                            async for chunk in read_parquet_file_async_streaming(
                                period_parquet_path
                            ):
                                if chunk:
                                    # Write to period-specific sheet
                                    excel_writer.write_period_data_chunk(
                                        chunk, period_label
                                    )
                                    total_rows += len(chunk)

                            log.info(
                                f"Completed period '{period_label}': {period_row_count} rows"
                            )
                        else:
                            log.info(f"Period '{period_label}' has no data, skipping")

                        # Allow other tasks to run
                        await asyncio.sleep(0)

                    except Exception as e:
                        log.error(
                            f"Error processing period '{period_label}': {e}",
                            exc_info=True,
                        )
                        # Continue with other periods
                        continue

                # Save Excel file
                saved_file_path = excel_writer.save()

                elapsed = time.time() - start_time
                log.info(
                    f"Export {export_id}: Completed period-separated processing - {total_rows} total rows in {elapsed:.2f}s"
                )

                # Signal that data processing is complete
                await queue.put(("data_complete", saved_file_path))

            except Exception as e:
                log.error(
                    f"Export {export_id}: Error in period-separated processing: {e}",
                    exc_info=True,
                )
                await queue.put(("error", str(e)))

        except Exception as e:
            log.error(
                f"Export {export_id}: Fatal error in period-separated processing: {e}",
                exc_info=True,
            )
            await queue.put(("error", str(e)))

    # Background task to stream file content
    async def stream_file_content():
        try:
            # Wait for data processing to complete
            while True:
                message_type, data = await queue.get()

                if message_type == "data_complete":
                    temp_file_path = data
                    break
                elif message_type == "error":
                    raise Exception(f"Data processing failed: {data}")

            # Stream the Excel file
            log.info(f"Export {export_id}: Starting file streaming")

            file_size = os.path.getsize(temp_file_path)
            position = 0

            with open(temp_file_path, "rb") as f:
                while position < file_size:
                    file_chunk = f.read(OPTIMAL_CHUNK_SIZE)
                    if not file_chunk:
                        break
                    position += len(file_chunk)
                    await queue.put(("file_chunk", file_chunk))

            await queue.put(("file_complete", None))
            log.info(f"Export {export_id}: File streaming completed")

        except Exception as e:
            log.error(
                f"Export {export_id}: Error in file streaming: {e}", exc_info=True
            )
            await queue.put(("error", str(e)))

    # Start background tasks
    data_task = asyncio.create_task(process_periods_and_generate_excel())
    stream_task = asyncio.create_task(stream_file_content())

    # Generator function to yield file chunks
    async def stream_generator():
        try:
            while True:
                message_type, data = await queue.get()

                if message_type == "file_chunk":
                    yield data
                elif message_type == "file_complete":
                    break
                elif message_type == "error":
                    raise Exception(f"Export failed: {data}")
        except Exception as e:
            log.error(f"Export {export_id}: Error in stream generator: {e}")
            raise
        finally:
            # Cancel background tasks if still running
            if not data_task.done():
                data_task.cancel()
            if not stream_task.done():
                stream_task.cancel()

    # Cleanup function
    async def cleanup_export():
        try:
            log.info(f"Export {export_id}: Starting cleanup")

            # Clean up Excel writer
            if excel_writer_for_cleanup:
                try:
                    excel_writer_for_cleanup.cleanup()
                except Exception as e:
                    log.warning(
                        f"Export {export_id}: Error cleaning up Excel writer: {e}"
                    )

            # Clean up temporary Parquet files
            if cleanup_source_files:
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            log.debug(
                                f"Export {export_id}: Cleaned up temp file: {temp_file}"
                            )
                    except Exception as e:
                        log.warning(
                            f"Export {export_id}: Error cleaning up temp file {temp_file}: {e}"
                        )

            log.info(f"Export {export_id}: Cleanup completed")

        except Exception as e:
            log.error(f"Export {export_id}: Error in cleanup: {e}")

    # Return streaming response
    return EnhancedStreamingResponse(
        content_generator=stream_generator(),
        filename=filename,
        background=BackgroundTask(cleanup_export),
    )
