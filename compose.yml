services:
  magic-gateway:
    image: localhost/magic-gateway:latest
    container_name: magic-gateway
    ports:
      - "8002:8002"
    volumes:
      - /tmp:/tmp
      - /reportoutput:/reportoutput
    environment:
      - APP_NAME=MagicGateway
      - APP_VERSION=0.4.4
      - DEBUG=True
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG

      # Security Settings
      - SECRET_KEY=AAAAB3NzaC1yc2EAAAABJQAAAQEA22vedKSuazmMvnr4UubqSrx95GsHW3gvF8htKQ2bCWYrg5t65ZNXT32sXLBjRgkIGAt5SltY6y0jLLZPRs5JUHfuxZ4pIrxkp
      - JWT_ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - REFRESH_TOKEN_EXPIRE_DAYS=7

      # LDAP Settings
      - LDAP_SERVER=**********
      - LDAP_DOMAIN=ICMR
      - LDAP_DOMAIN_FQDN=icmr.ru
      - LDAP_BASE_DN=dc=icmr,dc=ru

      # PostgreSQL Settings
      - POSTGRES_HOST=***********
      - POSTGRES_PORT=5432
      - POSTGRES_USER=msr.shinyproxy.svc
      - POSTGRES_PASSWORD=63wClvYSbfyCFH
      - POSTGRES_DB=pet
      - POSTGRES_MIN_CONNECTIONS=2
      - POSTGRES_MAX_CONNECTIONS=10

      # Logs Database Settings
      - LOGS_DB_HOST=***********
      - LOGS_DB_PORT=5432
      - LOGS_DB_USER=msr.shinyproxy.svc
      - LOGS_DB_PASSWORD=63wClvYSbfyCFH
      - LOGS_DB_NAME=logs
      - LOGS_DB_SCHEMA=api

      # ClickHouse Settings
      - CLICKHOUSE_HOST=***********
      - CLICKHOUSE_PORT=9000
      - CLICKHOUSE_HTTP_PORT=8123
      - CLICKHOUSE_USER=rnvaga
      - CLICKHOUSE_PASSWORD=Ldkh4hfh2f
      - CLICKHOUSE_DATABASE=pet
      - CLICKHOUSE_MIN_CONNECTIONS=2
      - CLICKHOUSE_MAX_CONNECTIONS=10

      # Query Settings
      - MAX_QUERY_EXECUTION_TIME=300
      - TRACK_QUERIES=True
    # restart: always
    command: ["poetry", "run", "uvicorn", "src.magic_gateway.main:app", "--host", "0.0.0.0", "--port", "8002", "--workers", "4"]